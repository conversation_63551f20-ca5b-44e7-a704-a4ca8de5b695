## prompt1  
你现在要做的是一个将文本转成表格的任务，请根据以下文本，归纳出一个或多个表格数据。具体步骤是  
1.提取出关系三元组。  
2.合并分析这些三元组。  
3.以csv的形式输出一个或多个表格  


### 员工信息表

| 姓名 | 部门 | 员工ID | 工号 | 角色/职位 |
|------|------|--------|------|-----------|
| 李思博士 | 研发部 | LS-004 | - | 领导人工智能核心算法团队 |
| 王五 | 销售团队 | WW-005 | - | 负责华北地区市场拓展 |
| 张三 | 技术支持部 | ZS-001 | - | 技术支持部总监 |
| 小赵 | 市场部 | - | - | 已离职（2024年2月10日） |

### 销售订单表

| 客户 | 订单号 | 金额 | 货币单位 | 签订日期 | 状态 | 经手人 |
|------|--------|------|----------|----------|------|--------|
| 环球集团 | - | 120.5万 | 美元 | 2024年3月15日 | 完成 | 张三 |
| 未来科技公司 | - | 55,000 | 欧元 | - | 完成 | - |
| 数据风暴公司 | ORD-9527 | 3,500 | 英镑 | - | 作废 | WW-005 |
| 快捷贸易 | - | 800 | 美元 | - | 搞定 | - |

### 项目信息表

| 项目名称 | 启动日期 | 负责人 | 部门 | 目标 | 当前状态 |
|----------|----------|--------|------|------|----------|
| 凤凰项目 | 2024年1月5日 | 李思博士 | 研发部 | 开发下一代数据分析引擎 | 进行中 |
| 方舟项目 | - | - | 研发部 | 内部IT系统升级 | 已完成 |
| 灯塔项目 | - | - | - | 探索性项目 | 待定 |


## prompt2
