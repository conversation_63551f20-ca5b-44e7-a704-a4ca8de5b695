## Text-to-Table Prompts

### 1. [Text-Tuple-Table: Towards Information Integration in Text-to-Table Generation via Global Tuple Extraction](https://arxiv.org/abs/2404.14215) EMNLP 2024

#### **Instruction** on Livesum

According to the live text, please count the number of:  
1.goals, 2. shots, 3.fouls, 4.yellow cards, 5.red cards,
6.corner kicks, 7.free kicks, and 8.offsides for each
team.  
Note that goals and saved attempts and blocked
attempts and missed attempts are considered shots.  
Handball and dangerous play are also considered foul.  
The second yellow card is also considered a red card.  
Penalty is also considered as free kicks.

#### **Prompt**

\<Instruction\>

Let’s do the following things:
1. Extract all the relevant events from the following
passage in (player name, team name, event) or (team
name, event) format.
2. Integrate these tuples.
3. Output a table with 2 rows in CSV format.

\<Text\>

### 2. [Map&Make: Schema Guided Text to Table Generation](https://arxiv.org/abs/2505.23174) ACL 2025

#### **Prompt**

1. **Atomization**
   You are an expert at converting unstructured, detailed textual inputs into highly structured and organized atomic statements.

   **TASK**:
   Decompose the given paragraphs or sentences into clear, self-contained, and highly detailed short atomic statements without losing any information. Each atomic statement should capture a single fact or action with maximum granularity.

   **INSTRUCTIONS**:
   - Capture only information explicitly stated in the input text.
   - No detail should be assumed, inferred, or added that is not present in the text.
   - Each atomic statement should contain only one key entity and one action or attribute.
   - If a sentence contains multiple pieces of information, decompose it further into more granular statements.
   - Eliminate ambiguity by resolving pronouns and ensuring each statement stands alone.
   - Preserve necessary context so each statement is meaningful on its own.
   - Represent numerical data and units exactly as given in the input text.
   - Ensure each statement conveys unique information without overlapping others.
   - Ensure statements are clear, direct, and free from unnecessary complexity.
   - Resolve pronouns to their corresponding nouns for clarity.
   - Maintain relationships between entities without combining multiple facts.

   **OUTPUT FORMAT**:
   ```
   <REASONING STEPS>

   ### Atomic Statements:
   <ATOMIC STATEMENT 1>
   <ATOMIC STATEMENT 2>
   ...
   ```

   **REASONING STEPS**:
   For each sentence, identify the entities and their corresponding events.

   **Sample Input**:
   The Oklahoma City Thunder (16 - 17) defeated the Phoenix Suns (18 - 16) 137 - 134 in overtime on Wednesday. Oklahoma City has won three of their last four games. Kevin Durant returned from a six - game absence due to an ankle sprain and put up a season - high 44 points in 40 minutes.

   **Step 1 - Sentence analysis**:
   Sentence 1: The Oklahoma City Thunder (16 - 17) defeated the Phoenix Suns (18 - 16) 137 - 134 in overtime on Wednesday.
   Here the entities are Oklahoma City Thunder and Phoenix Suns.
   Events are team records, game result, and total points.
   *Atomic sentences*:
   - The Oklahoma City Thunder's record is 16 wins.
   - The Oklahoma City Thunder's record is 17 losses.
   - The Phoenix Suns' record is 18 wins.
   - ...

   Repeat **Step 1** for all sentences in **Sample Input**

   **FINAL CHECKLIST**:
   - All information from the input text is included.
   - No information or calculation is added that is not present in the text.
   - Every fact and detail is accurately represented.
   - Statements are clear and can be understood independently.
   - Numerical data and units are formatted exactly as provided in the text.
   - Each statement directly reflects the input text without inferred details.
   - Pronouns are resolved; statements are unambiguous.
   - Each statement contains only one key entity and one action or attribute.
   - Do not number the statements or add extra formatting.
   - Provide the *OUTPUT* with *REASONING STEPS* in the specified format only.

2. **Schema Extraction**
   You are an expert at defining structural tables by identifying the relevant column headers and row headers from text.

   **TASK**:
   Given a set of atomic text statements, extract row and column headers to create a table schema.

   **INSTRUCTIONS**:
   - Read the statements carefully to identify all attributes, entities, and data points mentioned, whether explicitly stated or implicitly implied.
   - Determine the row headers (primary keys) and column headers required to represent the data comprehensively and concisely:
     - Row headers are the unique identifiers for individual rows (key entities).
     - Column headers are the attributes of the primary keys that represent different aspects or data points.
   - Include all explicit and implicit data points, ensuring no relevant information is overlooked.
   - Pay close attention to numerical data, even if it is presented within comparative statements or descriptions of events or related to specific categories or time periods mentioned in the text.
   - Explicit numerical data must always be captured as attributes where appropriate.
   - Implicit data points or recurring attributes must also be included.
   - Avoid adding actions as column headers but extract any data points associated with them.
   - Ensure that all numerical values are captured as attributes, even if they are related to specific time periods or events within the context. When encountering comparative statements or ratios like "X of Y", ensure you capture both 'X' and 'Y' as potentially distinct and relevant data points if they represent different aspects of an attribute.
   - Be attentive to granular details and avoid focusing solely on general or aggregate values if more specific data points are available in the text.

   **OUTPUT FORMAT**:
   ```
   <REASONING STEPS>

   ### Final Schema:
   {
       "<Table name>": {
           "row_headers": ["Row Header 1", ...],
           "column_headers": ["Column Header 1", ...]
       }
       ...
   }
   ```

   **REASONING STEPS**:
   **Sample input**:
   The Oklahoma City Thunder's record is 16 wins.
   The Oklahoma City Thunder's record is 17 losses.
   The Phoenix Suns' record is 18 wins.
   ...

   **Step 1 - Identify the context from all the statements to generate a short description**
   Thought: This is a summary of a basketball game played between the Oklahoma City Thunder and the Phoenix Suns and gives all the relevant statistics about the players and the games. Every statement is either about the team or one of the players hence it would be ideal to create to separate tables for them. One table for the teams, and one for the players.

   **Step 2 - Create a empty list of row and column headers for the tables. This list would be updated as we keep on processing the statements and will keep adding relevant column and row headers to the list.**
   *Intermediate output*:
   ```json
   {
       "Team": {
           "row_headers": [],
           "column_headers": []
       },
       "Player": {
           "row_headers": [],
           "column_headers": []
       }
   }
   ```

   **Step 3 - Process statements one by one and add relevant headers if not already present in the list.**
   *Statements processed*:
   1. The Oklahoma City Thunder's record is 16 wins.
   *Schema update*: Update in "Team" table - Row added: "Thunder" - Column added: "Wins"
   ...

   ### Final Schema:
   ```json
   {
       "Team": {
           "row_headers": ["Thunder", "Suns"],
           "column_headers": ["Wins", "Losses", "Total points"]
       },
       "Player": {
           "row_headers": ["Kevin Durant"],
           "column_headers": ["Points", "Minutes played"]
       }
   }
   ```

   **Final Output Instructions**:
   1. As shown in the illustration above, for *every given statement* return the updates done to the schema and generate the Team Table and Player Table schema.
   2. Do not return schema directly in any case.
   3. Provide the *OUTPUT* with *REASONING STEPS* in the specified format only.

3. **Table Generation**
   You are an expert in converting unstructured text into structured tables. Your task is to process a series of atomic statements and update a set of pre-defined table schemas. Note that you can be given more than one table to update. Follow the steps below to ensure accurate and complete table updates.

   **TASK**:
   **Given**:
   - *Statements*: A sequence of atomic statements.
   - *Schema*: A json object with table names and their row headers and column headers of the respective tables.

   **Your goal is to**:
   - Process each statement one by one.
   - Identify the correct set of table, row and column headers and the cell at that index to update based on the statement.
   - Update or add values to the tables accordingly.

   **OUTPUT FORMAT**:
   ```
   <REASONING STEPS>
   ### Final Output Tables:
   ### <TABLE NAME>
   | | <Column Header 1> | ... |
   | <Row Header 1> | <Cell Value for (Row Header 1, Column Header 1)> | ... |
   ...
   ```

   **REASONING STEPS**:
   Follow the given algorithm thoroughly:

   **ALGORITHM**
   For each statement in the input:
   1. **Identify Table**: Determine the correct table to be updated based on the statement.
   2. **Identify Row and Column**: Determine which set of row and columns headers have to be updated based on this table.
   3. **Update the Table**: If no value exists, update the value of the cell as per the statement.

   **Sample Input**:
   Statements:
   - The Oklahoma City Thunder's record is 16 wins.
   - The Oklahoma City Thunder's record is 17 losses.
   - ...

   Schema:
   ```json
   {
       "Team": {
           "row_headers": ["Thunder", "Suns"],
           "column_headers": ["Wins", "Losses", "Total points"]
       },
       "Player": {
           "row_headers": ["Kevin Durant"],
           "column_headers": ["Points", "Minutes played"]
       }
   }
   ```

   **Step 1**: Initial tables (Empty Tables):
   ### Team:
   | Team | Wins | Losses | Total Points |
   | Thunder | None | None | None |
   | Suns | None | None | None |

   ### Player:
   | Player | Points | Minutes Played |
   | Kevin Durant | None | None |

   **Step 2**: *Statement processed*:
   The Oklahoma City Thunder's record is 16 wins.
   *Updates*:
   - Table: Team
   - Row: Thunder
   - Column: Wins
   - Value: 16
   ...

   ### Final Output Tables:
   ### Team
   | Team | Wins | Losses | Total Points |
   | Thunder | 16 | 17 | None |
   | Suns | 18 | None | 134 |

   ### Player
   | Player | Points | Minutes Played |
   | Kevin Durant | 44 | 40 |

   **FINAL CHECKLIST**:
   - Follow these guidelines to generate tables and return the final state of the table after processing all the statements.
   - Ensure all sentences are processed and for every statement return the update and revised state of the updated cells as shown in the example. Return the final table in the exact specified format starting with ### Final Table.
   - Do not generate the final table directly in any case.
   - No need to generate the intermediate table states, just return the final table at the end.
   - Ensure the table is concise, well-structured, and contains all information from the input.

   **Final Output Instructions**:
   1. **Handle Missing Data**: If a column value is not present in the statements, keep it as None.
   2. **Structural Integrity**: Do not add or remove any rows or columns unless explicitly instructed by the data. Ensure uniformity in the format of data across the table.
   3. **Table formatting**: Use "|" to separate cells. Provide the *OUTPUT* with *REASONING STEPS* in the specified format only.


### 3. CoT & One-shot

#### **Prompt**

You are expert at converting text to tables.

**Task:** Given a text description, generate and fill tables.
Think step by step and output the Team table and the Player Table.

**Sample Illustration:**

**Input:**
The Oklahoma City Thunder (16 - 17) defeated the Phoenix Suns (18 - 16) 137 - 134 in overtime on Wednesday. Oklahoma City has won three of their last four games. Kevin Durant returned from a six - game absence due to an ankle sprain and put up a season - high 44 points in 40 minutes.

**Output:**
### Team
| Team | Wins | Losses | Total Points |
| Thunder | 16 | 17 | None |
| Suns | 18 | None | 134 |

### Player
| Player | Points | Minutes Played |
| Kevin Durant | 44 | 40 |

**Output format:**
### Team
| Team | <Column Header 1> | ... |
| <Row Header 1> | <Cell Value for (Row Header 1, Column Header 1)> | ... |
...

### Player
| Player | <Column Header 1> | ... |
| <Row Header 1> | <Cell Value for (Row Header 1, Column Header 1)> | ... |
...

**Format Requirements:**
- Table name leads with a ###, followed by the table where values are separated by the symbol '|', and rows are separated by '\n'.
- Use '|' as the only separator/delimiter.
- Empty cell values are filled as "None".
- Provide the output in the specified format only.
