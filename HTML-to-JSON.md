# HTML-to-Json

## 问题定义

输入：HTML，

输出：json数据。

要求从HTML内容中提取出尽可能详细的信息，并且包含层次结构。

![image-20250810224436885](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250810224436885.png)

传统的实现方式应该是编程人员分析网页内容，然后制定合适的数据结构，再写解析网页的代码进行提取，费时费力，并且要针对不同的页面写不同的代码。

例如：

```python
    comments_dict = []
    tree = etree.HTML(get_html(url))
    comment_list = tree.xpath('//div[@class="comment-item"]')
    if len(comment_list) == 0:
        return comments_dict
    for comment_div in comment_list:
        try:
            name = comment_div.xpath('.//span[@class="comment-info"]/a/text()')[0].strip()
        except:
            name = ''
        try:
            content = comment_div.xpath('.//p[@class="comment-content"]/span/text()')[0].strip()
        except:
            continue
        upvote = comment_div.xpath('.//span[@class="votes vote-count"]/text()')[0].strip()
        time = comment_div.xpath('.//span[@class="comment-time"]/@title')[0]
        try:
            location = comment_div.xpath('.//span[@class="comment-location"]/text()')[0].strip()
        except:
            location = ''
        try:
            star_attribute = comment_div.xpath('.//span[contains(@class,"rating")]/@class')[0]
            stars = re.search(r'\d+', star_attribute).group()[0]
        except:
            stars = 0

        comments_dict.append({
            'name': name,
            'content': content,
            'upvote': upvote,
            'time': time,
            # 'location': location,
            'stars': stars
        })
    return comments_dict
```



## 分析

**为什么想要做这个任务？**





目前似乎没有太多相关研究？

有一些从HTML文档提取数据的工作，不过都比较基础，比如2025年的《Next Evaluation of Traditional and LLM Web Data Record Extraction》仅仅需要从HTML中提取出数据，不满足这里的场景和要求。

有能实现类似功能的工具，如Crawl4AI，不过更多是工程项目而缺乏深度学术评估，仅仅通过预定义prompt和json结构来依靠大模型本身实现。

有待进一步分析调研相关工作。







## 文献调研



#### 1. [Next Evaluation of Traditional and LLM Web Data Record Extraction](https://arxiv.org/abs/2505.17125) arxiv 2025

- 评估传统方法与LLM在web数据提取的性能

任务：

将网页数据记录提取任务形式化为在网页的DOM树中识别重复的语义相关元素集合。任务目标是从HTML文档中分割出这些记录集合。

输入：

```html
<div class="product">
<div class="image">
<img src="a.jpg"/>
<span class="caption">A beautiful image</span>
</div>
<div class="info">
<span class="name">Camera</span>
<span class="price">$399</span>
</div>
</div>
```

output:

```txt
{
/div[1]/div[1]/span[1]
,
/div[1]/div[2]/span[1]
,
/div[1]/div[2]/span[2]
}
--------------------------------------------------------------------
(A beautiful image, Camera, $399)
```



### 2. [Using LLMs for the Extraction and Normalization of Product Attribute Values](https://arxiv.org/abs/2403.02130) ADBIS 2024

从电子商务网站的产品标题和描述中提取和规范化产品属性值

输入：

```txt
HP – 6280-59-B21 - 3TB 3G SATA 7.2K rpm LFF (3.5-inch) Midline 1yr Hard Drive
```

输出：

```json
"Brand": "HP" --> "Hewlett-Packard"
"Product Type": "Hard Drive" --> "Storage Solutions"
"Rotational Speed": "7.2K" --> "7200"
"Part Number": "6280-59-B21" --> "628059B21"
```



#### 3. [ReaderLM-v2: Small Language Model for HTML to Markdown and JSON](https://arxiv.org/abs/2503.01151) arxiv 2025

- 一个1.5B参数的小型LLM，专用于从长上下文HTML中提取结构化内容，包括直接转换为JSON格式

